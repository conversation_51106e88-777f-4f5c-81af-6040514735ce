# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Docker
.dockerignore
Dockerfile*
docker-compose*
docker/

# Git
.git/
.gitignore
.github/

# Documentation
docs/
*.md

# Tests
tests/
test_*.py
*_test.py

# Logs
logs/
*.log

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Misc
.mypy_cache/
