"""
Admin dashboard routes for the Management Service.

This module provides admin-only endpoints for:
- Dashboard metrics and analytics
- User detail information
- System statistics
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.security import require_roles
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()


@router.get("/admin/dashboard")
async def get_admin_dashboard(
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get comprehensive admin dashboard metrics.
    
    Returns:
        Dictionary containing all dashboard metrics including:
        - User metrics (total, daily joins, active today)
        - Task metrics (total, created today, completed today)
        - Activity distribution by type and status
        - Performance metrics
    """
    try:
        loggers.info("Getting admin dashboard metrics")
        
        # Get current date boundaries
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items
        
        # User Metrics Aggregation
        user_metrics_pipeline = [
            {
                "$facet": {
                    "total_users": [{"$count": "count"}],
                    "users_joined_today": [
                        {"$match": {"created_at": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "users_active_today": [
                        {"$match": {"last_login": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "users_by_role": [
                        {"$group": {"_id": "$role", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ]
                }
            }
        ]
        
        # Task Sets Metrics Aggregation
        task_sets_pipeline = [
            {
                "$facet": {
                    "total_task_sets": [{"$count": "count"}],
                    "task_sets_created_today": [
                        {"$match": {"created_at": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "task_sets_completed_today": [
                        {"$match": {
                            "completed_at": {"$gte": today_start},
                            "status": "completed"
                        }},
                        {"$count": "count"}
                    ],
                    "task_sets_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_sets_by_input_type": [
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "completion_stats": [
                        {"$match": {"status": "completed"}},
                        {"$group": {
                            "_id": None,
                            "avg_completion_time": {
                                "$avg": {
                                    "$subtract": ["$completed_at", "$created_at"]
                                }
                            },
                            "avg_score": {"$avg": "$scored"},
                            "avg_total_score": {"$avg": "$total_score"}
                        }}
                    ]
                }
            }
        ]
        
        # Task Items Metrics Aggregation
        task_items_pipeline = [
            {
                "$facet": {
                    "total_task_items": [{"$count": "count"}],
                    "task_items_created_today": [
                        {"$match": {"created_at": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "task_items_completed_today": [
                        {"$match": {
                            "answered_at": {"$gte": today_start},
                            "status": "completed"
                        }},
                        {"$count": "count"}
                    ],
                    "task_items_by_type": [
                        {"$group": {"_id": "$type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_items_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "verification_stats": [
                        {"$group": {
                            "_id": "$verification_status",
                            "count": {"$sum": 1}
                        }},
                        {"$sort": {"count": -1}}
                    ]
                }
            }
        ]
        
        # Execute aggregations
        user_results = await (await users_collection.aggregate(user_metrics_pipeline)).to_list(1)
        task_sets_results = await (await task_sets_collection.aggregate(task_sets_pipeline)).to_list(1)
        task_items_results = await (await task_items_collection.aggregate(task_items_pipeline)).to_list(1)
        
        # Extract results with safe defaults
        user_data = user_results[0] if user_results else {}
        task_sets_data = task_sets_results[0] if task_sets_results else {}
        task_items_data = task_items_results[0] if task_items_results else {}
        
        # Helper function to safely get count
        def safe_count(data_list):
            return data_list[0]["count"] if data_list and len(data_list) > 0 else 0
        
        # Build response
        dashboard_data = {
            "timestamp": now.isoformat(),
            "user_metrics": {
                "total_users": safe_count(user_data.get("total_users", [])),
                "users_joined_today": safe_count(user_data.get("users_joined_today", [])),
                "users_active_today": safe_count(user_data.get("users_active_today", [])),
                "users_by_role": user_data.get("users_by_role", [])
            },
            "task_set_metrics": {
                "total_task_sets": safe_count(task_sets_data.get("total_task_sets", [])),
                "task_sets_created_today": safe_count(task_sets_data.get("task_sets_created_today", [])),
                "task_sets_completed_today": safe_count(task_sets_data.get("task_sets_completed_today", [])),
                "task_sets_by_status": task_sets_data.get("task_sets_by_status", []),
                "task_sets_by_input_type": task_sets_data.get("task_sets_by_input_type", []),
                "completion_stats": task_sets_data.get("completion_stats", [])
            },
            "task_item_metrics": {
                "total_task_items": safe_count(task_items_data.get("total_task_items", [])),
                "task_items_created_today": safe_count(task_items_data.get("task_items_created_today", [])),
                "task_items_completed_today": safe_count(task_items_data.get("task_items_completed_today", [])),
                "task_items_by_type": task_items_data.get("task_items_by_type", []),
                "task_items_by_status": task_items_data.get("task_items_by_status", []),
                "verification_stats": task_items_data.get("verification_stats", [])
            }
        }
        
        # Calculate derived metrics
        total_users = dashboard_data["user_metrics"]["total_users"]
        users_joined_today = dashboard_data["user_metrics"]["users_joined_today"]
        users_active_today = dashboard_data["user_metrics"]["users_active_today"]
        
        dashboard_data["derived_metrics"] = {
            "user_growth_rate_today": round((users_joined_today / total_users * 100), 2) if total_users > 0 else 0,
            "user_activity_rate_today": round((users_active_today / total_users * 100), 2) if total_users > 0 else 0,
            "task_completion_rate": 0  # Will be calculated if needed
        }
        
        loggers.info("Admin dashboard metrics retrieved successfully")
        return dashboard_data
        
    except Exception as e:
        loggers.error(f"Error getting admin dashboard metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard metrics: {str(e)}")


@router.get("/admin/dashboard/user/{user_id}")
async def get_user_details(
    user_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get detailed information about a specific user.
    
    Args:
        user_id: The ID of the user to get details for
        
    Returns:
        Dictionary containing comprehensive user information including:
        - Basic user info (username, role, join date, etc.)
        - Task activity (total tasks, completed tasks, scores)
        - Recent activity timeline
        - Performance metrics
    """
    try:
        loggers.info(f"Getting detailed info for user: {user_id}")
        
        # Validate user_id format
        try:
            user_object_id = ObjectId(user_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid user ID format")
        
        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items
        
        # Get user basic info
        user = await users_collection.find_one({"_id": user_object_id})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # User task activity aggregation
        user_activity_pipeline = [
            {"$match": {"user_id": user_id}},
            {
                "$facet": {
                    "task_sets_summary": [
                        {"$group": {
                            "_id": None,
                            "total_task_sets": {"$sum": 1},
                            "completed_task_sets": {
                                "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                            },
                            "total_score_earned": {"$sum": "$scored"},
                            "total_possible_score": {"$sum": "$total_score"},
                            "avg_score": {"$avg": "$scored"}
                        }}
                    ],
                    "task_sets_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_sets_by_type": [
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "recent_activity": [
                        {"$sort": {"created_at": -1}},
                        {"$limit": 10},
                        {"$project": {
                            "_id": 1,
                            "input_type": 1,
                            "status": 1,
                            "created_at": 1,
                            "completed_at": 1,
                            "scored": 1,
                            "total_score": 1,
                            "total_tasks": 1
                        }}
                    ]
                }
            }
        ]
        
        # Execute user activity aggregation
        activity_results = await task_sets_collection.aggregate(user_activity_pipeline).to_list(1)
        activity_data = activity_results[0] if activity_results else {}
        
        # Get task items created by this user (if any)
        user_created_tasks = await task_items_collection.count_documents({"created_by": user_id})
        
        # Build user details response
        user_details = {
            "user_info": {
                "id": str(user["_id"]),
                "username": user.get("username"),
                "role": user.get("role"),
                "email": user.get("email"),
                "full_name": user.get("full_name"),
                "created_at": user.get("created_at"),
                "last_login": user.get("last_login"),
                "previous_login": user.get("previous_login"),
                "onboarding_completed": user.get("onboarding_completed", False),
                "age": user.get("age"),
                "difficulty_level": user.get("difficulty_level"),
                "preferred_topics": user.get("preferred_topics", [])
            },
            "activity_summary": activity_data.get("task_sets_summary", [{}])[0] if activity_data.get("task_sets_summary") else {},
            "task_distribution": {
                "by_status": activity_data.get("task_sets_by_status", []),
                "by_type": activity_data.get("task_sets_by_type", [])
            },
            "recent_activity": activity_data.get("recent_activity", []),
            "content_creation": {
                "tasks_created": user_created_tasks
            }
        }
        
        # Convert ObjectIds to strings in recent activity
        for activity in user_details["recent_activity"]:
            activity["_id"] = str(activity["_id"])
        
        loggers.info(f"User details retrieved successfully for user: {user_id}")
        return user_details
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting user details for {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user details: {str(e)}")
