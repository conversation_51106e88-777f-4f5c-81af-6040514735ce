"""
Admin dashboard routes for the Management Service.

This module provides admin-only endpoints for:
- Dashboard metrics and analytics
- User detail information
- System statistics
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.security import require_roles
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()


@router.get("/admin/dashboard")
async def get_admin_dashboard(
    start_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get comprehensive admin dashboard metrics.
    
    Returns:
        Dictionary containing all dashboard metrics including:
        - User metrics (total, daily joins, active today)
        - Task metrics (total, created today, completed today)
        - Activity distribution by type and status
        - Performance metrics
    """
    try:
        loggers.info("Getting admin dashboard metrics")
        
        # Get current date boundaries
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items
        
        # User Metrics Aggregation
        user_metrics_pipeline = [
            {
                "$facet": {
                    "total_users": [{"$count": "count"}],
                    "users_joined_today": [
                        {"$match": {"created_at": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "users_active_today": [
                        {"$match": {"last_login": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "users_by_role": [
                        {"$group": {"_id": "$role", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ]
                }
            }
        ]
        
        # Task Sets Metrics Aggregation
        task_sets_pipeline = [
            {
                "$facet": {
                    "total_task_sets": [{"$count": "count"}],
                    "task_sets_created_today": [
                        {"$match": {"created_at": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "task_sets_completed_today": [
                        {"$match": {
                            "completed_at": {"$gte": today_start},
                            "status": "completed"
                        }},
                        {"$count": "count"}
                    ],
                    "task_sets_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_sets_by_input_type": [
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "completion_stats": [
                        {"$match": {"status": "completed"}},
                        {"$group": {
                            "_id": None,
                            "avg_completion_time": {
                                "$avg": {
                                    "$subtract": ["$completed_at", "$created_at"]
                                }
                            },
                            "avg_score": {"$avg": "$scored"},
                            "avg_total_score": {"$avg": "$total_score"}
                        }}
                    ]
                }
            }
        ]
        
        # Task Items Metrics Aggregation
        task_items_pipeline = [
            {
                "$facet": {
                    "total_task_items": [{"$count": "count"}],
                    "task_items_created_today": [
                        {"$match": {"created_at": {"$gte": today_start}}},
                        {"$count": "count"}
                    ],
                    "task_items_completed_today": [
                        {"$match": {
                            "answered_at": {"$gte": today_start},
                            "status": "completed"
                        }},
                        {"$count": "count"}
                    ],
                    "task_items_by_type": [
                        {"$group": {"_id": "$type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_items_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "verification_stats": [
                        {"$group": {
                            "_id": "$verification_status",
                            "count": {"$sum": 1}
                        }},
                        {"$sort": {"count": -1}}
                    ]
                }
            }
        ]
        
        # Execute aggregations
        user_results = await (await users_collection.aggregate(user_metrics_pipeline)).to_list(1)
        task_sets_results = await (await task_sets_collection.aggregate(task_sets_pipeline)).to_list(1)
        task_items_results = await (await task_items_collection.aggregate(task_items_pipeline)).to_list(1)
        
        # Extract results with safe defaults
        user_data = user_results[0] if user_results else {}
        task_sets_data = task_sets_results[0] if task_sets_results else {}
        task_items_data = task_items_results[0] if task_items_results else {}
        
        # Helper function to safely get count
        def safe_count(data_list):
            return data_list[0]["count"] if data_list and len(data_list) > 0 else 0
        
        # Build response
        dashboard_data = {
            "timestamp": now.isoformat(),
            "user_metrics": {
                "total_users": safe_count(user_data.get("total_users", [])),
                "users_joined_today": safe_count(user_data.get("users_joined_today", [])),
                "users_active_today": safe_count(user_data.get("users_active_today", [])),
                "users_by_role": user_data.get("users_by_role", [])
            },
            "task_set_metrics": {
                "total_task_sets": safe_count(task_sets_data.get("total_task_sets", [])),
                "task_sets_created_today": safe_count(task_sets_data.get("task_sets_created_today", [])),
                "task_sets_completed_today": safe_count(task_sets_data.get("task_sets_completed_today", [])),
                "task_sets_by_status": task_sets_data.get("task_sets_by_status", []),
                "task_sets_by_input_type": task_sets_data.get("task_sets_by_input_type", []),
                "completion_stats": task_sets_data.get("completion_stats", [])
            },
            "task_item_metrics": {
                "total_task_items": safe_count(task_items_data.get("total_task_items", [])),
                "task_items_created_today": safe_count(task_items_data.get("task_items_created_today", [])),
                "task_items_completed_today": safe_count(task_items_data.get("task_items_completed_today", [])),
                "task_items_by_type": task_items_data.get("task_items_by_type", []),
                "task_items_by_status": task_items_data.get("task_items_by_status", []),
                "verification_stats": task_items_data.get("verification_stats", [])
            }
        }
        
        # Calculate derived metrics
        total_users = dashboard_data["user_metrics"]["total_users"]
        users_joined_today = dashboard_data["user_metrics"]["users_joined_today"]
        users_active_today = dashboard_data["user_metrics"]["users_active_today"]
        
        dashboard_data["derived_metrics"] = {
            "user_growth_rate_today": round((users_joined_today / total_users * 100), 2) if total_users > 0 else 0,
            "user_activity_rate_today": round((users_active_today / total_users * 100), 2) if total_users > 0 else 0,
            "task_completion_rate": 0  # Will be calculated if needed
        }
        
        loggers.info("Admin dashboard metrics retrieved successfully")
        return dashboard_data
        
    except Exception as e:
        loggers.error(f"Error getting admin dashboard metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard metrics: {str(e)}")


@router.get("/admin/dashboard/user/{user_id}")
async def get_user_details(
    user_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get detailed information about a specific user.
    
    Args:
        user_id: The ID of the user to get details for
        
    Returns:
        Dictionary containing comprehensive user information including:
        - Basic user info (username, role, join date, etc.)
        - Task activity (total tasks, completed tasks, scores)
        - Recent activity timeline
        - Performance metrics
    """
    try:
        loggers.info(f"Getting detailed info for user: {user_id}")
        
        # Validate user_id format
        try:
            user_object_id = ObjectId(user_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid user ID format")
        
        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items
        
        # Get user basic info
        user = await users_collection.find_one({"_id": user_object_id})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # User task activity aggregation
        user_activity_pipeline = [
            {"$match": {"user_id": user_id}},
            {
                "$facet": {
                    "task_sets_summary": [
                        {"$group": {
                            "_id": None,
                            "total_task_sets": {"$sum": 1},
                            "completed_task_sets": {
                                "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                            },
                            "total_score_earned": {"$sum": "$scored"},
                            "total_possible_score": {"$sum": "$total_score"},
                            "avg_score": {"$avg": "$scored"}
                        }}
                    ],
                    "task_sets_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_sets_by_type": [
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "recent_activity": [
                        {"$sort": {"created_at": -1}},
                        {"$limit": 10},
                        {"$project": {
                            "_id": 1,
                            "input_type": 1,
                            "status": 1,
                            "created_at": 1,
                            "completed_at": 1,
                            "scored": 1,
                            "total_score": 1,
                            "total_tasks": 1
                        }}
                    ]
                }
            }
        ]
        
        # Execute user activity aggregation
        activity_results = await task_sets_collection.aggregate(user_activity_pipeline).to_list(1)
        activity_data = activity_results[0] if activity_results else {}
        
        # Get task items created by this user (if any)
        user_created_tasks = await task_items_collection.count_documents({"created_by": user_id})
        
        # Build user details response
        user_details = {
            "user_info": {
                "id": str(user["_id"]),
                "username": user.get("username"),
                "role": user.get("role"),
                "email": user.get("email"),
                "full_name": user.get("full_name"),
                "created_at": user.get("created_at"),
                "last_login": user.get("last_login"),
                "previous_login": user.get("previous_login"),
                "onboarding_completed": user.get("onboarding_completed", False),
                "age": user.get("age"),
                "difficulty_level": user.get("difficulty_level"),
                "preferred_topics": user.get("preferred_topics", [])
            },
            "activity_summary": activity_data.get("task_sets_summary", [{}])[0] if activity_data.get("task_sets_summary") else {},
            "task_distribution": {
                "by_status": activity_data.get("task_sets_by_status", []),
                "by_type": activity_data.get("task_sets_by_type", [])
            },
            "recent_activity": activity_data.get("recent_activity", []),
            "content_creation": {
                "tasks_created": user_created_tasks
            }
        }
        
        # Convert ObjectIds to strings in recent activity
        for activity in user_details["recent_activity"]:
            activity["_id"] = str(activity["_id"])
        
        loggers.info(f"User details retrieved successfully for user: {user_id}")
        return user_details
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting user details for {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user details: {str(e)}")


@router.get("/dashboard")
async def get_dashboard_overview(
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get basic dashboard overview metrics.

    Returns:
        Dictionary containing basic overview metrics
    """
    try:
        loggers.info("Getting dashboard overview")

        # Get current date
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets

        # Basic counts
        total_users = await users_collection.count_documents({})
        users_joined_today = await users_collection.count_documents({"created_at": {"$gte": today_start}})
        users_active_today = await users_collection.count_documents({"last_login": {"$gte": today_start}})

        total_task_sets = await task_sets_collection.count_documents({})
        task_sets_created_today = await task_sets_collection.count_documents({"created_at": {"$gte": today_start}})
        task_sets_completed_today = await task_sets_collection.count_documents({
            "completed_at": {"$gte": today_start},
            "status": "completed"
        })

        return {
            "timestamp": now.isoformat(),
            "overview": {
                "total_users": total_users,
                "users_joined_today": users_joined_today,
                "users_active_today": users_active_today,
                "total_task_sets": total_task_sets,
                "task_sets_created_today": task_sets_created_today,
                "task_sets_completed_today": task_sets_completed_today
            }
        }

    except Exception as e:
        loggers.error(f"Error getting dashboard overview: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard overview: {str(e)}")


@router.get("/dashboard/users")
async def get_users_metrics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD), defaults to 7 days ago"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD), defaults to today"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get user metrics for date range with daily breakdown for graphs.
    Default range is past 7 days.

    Returns:
        Dictionary containing user metrics with daily breakdown
    """
    try:
        loggers.info(f"Getting user metrics from {start_date} to {end_date}")

        # Parse dates or use defaults
        now = datetime.now(timezone.utc)

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        else:
            end_dt = now

        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            start_dt = end_dt - timedelta(days=7)

        # Get database collection
        users_collection = user_tenant.async_db.users

        # Daily user registrations pipeline
        daily_registrations_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$created_at"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]

        # Daily active users pipeline
        daily_active_pipeline = [
            {
                "$match": {
                    "last_login": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$last_login"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]

        # Users by role pipeline
        users_by_role_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": "$role",
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"count": -1}}
        ]

        # Execute aggregations
        daily_registrations = await (await users_collection.aggregate(daily_registrations_pipeline)).to_list(None)
        daily_active = await (await users_collection.aggregate(daily_active_pipeline)).to_list(None)
        users_by_role = await (await users_collection.aggregate(users_by_role_pipeline)).to_list(None)

        # Get total counts
        total_users = await users_collection.count_documents({})
        users_in_period = await users_collection.count_documents({
            "created_at": {"$gte": start_dt, "$lte": end_dt}
        })

        return {
            "timestamp": now.isoformat(),
            "date_range": {
                "start_date": start_dt.strftime("%Y-%m-%d"),
                "end_date": end_dt.strftime("%Y-%m-%d")
            },
            "summary": {
                "total_users": total_users,
                "users_in_period": users_in_period
            },
            "daily_data": {
                "registrations": daily_registrations,
                "active_users": daily_active
            },
            "distribution": {
                "by_role": users_by_role
            }
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        loggers.error(f"Error getting user metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user metrics: {str(e)}")


@router.get("/dashboard/task-sets")
async def get_task_sets_metrics(
    date: Optional[str] = Query(None, description="Date filter (YYYY-MM-DD), defaults to today"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get task sets metrics for a specific date.
    Default is today's date.

    Returns:
        Dictionary containing task sets metrics for the specified date
    """
    try:
        loggers.info(f"Getting task sets metrics for date: {date}")

        # Parse date or use today
        now = datetime.now(timezone.utc)

        if date:
            target_date = datetime.strptime(date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            target_date = now

        # Get date boundaries
        date_start = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        date_end = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Get database collections
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items

        # Task sets metrics pipeline
        task_sets_pipeline = [
            {
                "$facet": {
                    "created_today": [
                        {"$match": {"created_at": {"$gte": date_start, "$lte": date_end}}},
                        {"$count": "count"}
                    ],
                    "completed_today": [
                        {
                            "$match": {
                                "completed_at": {"$gte": date_start, "$lte": date_end},
                                "status": "completed"
                            }
                        },
                        {"$count": "count"}
                    ],
                    "by_input_type": [
                        {"$match": {"created_at": {"$gte": date_start, "$lte": date_end}}},
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "by_status": [
                        {"$match": {"created_at": {"$gte": date_start, "$lte": date_end}}},
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "hourly_creation": [
                        {"$match": {"created_at": {"$gte": date_start, "$lte": date_end}}},
                        {
                            "$group": {
                                "_id": {"$hour": "$created_at"},
                                "count": {"$sum": 1}
                            }
                        },
                        {"$sort": {"_id": 1}}
                    ],
                    "performance_stats": [
                        {
                            "$match": {
                                "created_at": {"$gte": date_start, "$lte": date_end},
                                "status": "completed"
                            }
                        },
                        {
                            "$group": {
                                "_id": None,
                                "avg_score": {"$avg": "$scored"},
                                "avg_total_score": {"$avg": "$total_score"},
                                "avg_completion_time": {
                                    "$avg": {
                                        "$subtract": ["$completed_at", "$created_at"]
                                    }
                                },
                                "total_tasks_completed": {"$sum": "$total_tasks"},
                                "total_score_earned": {"$sum": "$scored"}
                            }
                        }
                    ]
                }
            }
        ]

        # Task items created today (by generation type)
        task_items_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": date_start, "$lte": date_end}
                }
            },
            {
                "$facet": {
                    "total_created": [{"$count": "count"}],
                    "by_type": [
                        {"$group": {"_id": "$type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "by_verification_status": [
                        {"$group": {"_id": "$verification_status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ]
                }
            }
        ]

        # Execute aggregations
        task_sets_results = await (await task_sets_collection.aggregate(task_sets_pipeline)).to_list(1)
        task_items_results = await (await task_items_collection.aggregate(task_items_pipeline)).to_list(1)

        # Extract results
        task_sets_data = task_sets_results[0] if task_sets_results else {}
        task_items_data = task_items_results[0] if task_items_results else {}

        # Helper function to safely get count
        def safe_count(data_list):
            return data_list[0]["count"] if data_list and len(data_list) > 0 else 0

        # Get total counts for context
        total_task_sets = await task_sets_collection.count_documents({})
        total_task_items = await task_items_collection.count_documents({})

        return {
            "timestamp": now.isoformat(),
            "date": target_date.strftime("%Y-%m-%d"),
            "summary": {
                "total_task_sets_all_time": total_task_sets,
                "total_task_items_all_time": total_task_items,
                "task_sets_created_today": safe_count(task_sets_data.get("created_today", [])),
                "task_sets_completed_today": safe_count(task_sets_data.get("completed_today", [])),
                "task_items_created_today": safe_count(task_items_data.get("total_created", []))
            },
            "task_sets": {
                "by_input_type": task_sets_data.get("by_input_type", []),
                "by_status": task_sets_data.get("by_status", []),
                "hourly_creation": task_sets_data.get("hourly_creation", []),
                "performance_stats": task_sets_data.get("performance_stats", [])
            },
            "task_items": {
                "by_type": task_items_data.get("by_type", []),
                "by_verification_status": task_items_data.get("by_verification_status", [])
            }
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        loggers.error(f"Error getting task sets metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve task sets metrics: {str(e)}")
