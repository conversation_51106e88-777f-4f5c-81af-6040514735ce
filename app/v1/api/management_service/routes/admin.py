"""
Admin dashboard routes for the Management Service.

This module provides admin-only endpoints for:
- Dashboard metrics and analytics
- User detail information
- System statistics
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.security import require_roles
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()



@router.get("/admin/dashboard/user/{user_id}")
async def get_user_details(
    user_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get detailed information about a specific user.
    
    Args:
        user_id: The ID of the user to get details for
        
    Returns:
        Dictionary containing comprehensive user information including:
        - Basic user info (username, role, join date, etc.)
        - Task activity (total tasks, completed tasks, scores)
        - Recent activity timeline
        - Performance metrics
    """
    try:
        loggers.info(f"Getting detailed info for user: {user_id}")
        
        # Validate user_id format
        try:
            user_object_id = ObjectId(user_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid user ID format")
        
        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items
        
        # Get user basic info
        user = await users_collection.find_one({"_id": user_object_id})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # User task activity aggregation
        user_activity_pipeline = [
            {"$match": {"user_id": user_id}},
            {
                "$facet": {
                    "task_sets_summary": [
                        {"$group": {
                            "_id": None,
                            "total_task_sets": {"$sum": 1},
                            "completed_task_sets": {
                                "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                            },
                            "total_score_earned": {"$sum": "$scored"},
                            "total_possible_score": {"$sum": "$total_score"},
                            "avg_score": {"$avg": "$scored"}
                        }}
                    ],
                    "task_sets_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_sets_by_type": [
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "recent_activity": [
                        {"$sort": {"created_at": -1}},
                        {"$limit": 10},
                        {"$project": {
                            "_id": 1,
                            "input_type": 1,
                            "status": 1,
                            "created_at": 1,
                            "completed_at": 1,
                            "scored": 1,
                            "total_score": 1,
                            "total_tasks": 1
                        }}
                    ]
                }
            }
        ]
        
        # Execute user activity aggregation
        activity_results = await task_sets_collection.aggregate(user_activity_pipeline).to_list(1)
        activity_data = activity_results[0] if activity_results else {}
        
        # Get task items created by this user (if any)
        user_created_tasks = await task_items_collection.count_documents({"created_by": user_id})
        
        # Build user details response
        user_details = {
            "user_info": {
                "id": str(user["_id"]),
                "username": user.get("username"),
                "role": user.get("role"),
                "email": user.get("email"),
                "full_name": user.get("full_name"),
                "created_at": user.get("created_at"),
                "last_login": user.get("last_login"),
                "previous_login": user.get("previous_login"),
                "onboarding_completed": user.get("onboarding_completed", False),
                "age": user.get("age"),
                "difficulty_level": user.get("difficulty_level"),
                "preferred_topics": user.get("preferred_topics", [])
            },
            "activity_summary": activity_data.get("task_sets_summary", [{}])[0] if activity_data.get("task_sets_summary") else {},
            "task_distribution": {
                "by_status": activity_data.get("task_sets_by_status", []),
                "by_type": activity_data.get("task_sets_by_type", [])
            },
            "recent_activity": activity_data.get("recent_activity", []),
            "content_creation": {
                "tasks_created": user_created_tasks
            }
        }
        
        # Convert ObjectIds to strings in recent activity
        for activity in user_details["recent_activity"]:
            activity["_id"] = str(activity["_id"])
        
        loggers.info(f"User details retrieved successfully for user: {user_id}")
        return user_details
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting user details for {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user details: {str(e)}")


@router.get("/dashboard")
async def get_dashboard_overview(
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get basic dashboard overview metrics.

    Returns:
        Dictionary containing basic overview metrics
    """
    try:
        loggers.info("Getting dashboard overview")

        # Get current date
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets

        # Basic counts
        total_users = await users_collection.count_documents({})
        users_joined_today = await users_collection.count_documents({"created_at": {"$gte": today_start}})
        users_active_today = await users_collection.count_documents({"last_login": {"$gte": today_start}})

        total_task_sets = await task_sets_collection.count_documents({})
        task_sets_created_today = await task_sets_collection.count_documents({"created_at": {"$gte": today_start}})
        task_sets_completed_today = await task_sets_collection.count_documents({
            "completed_at": {"$gte": today_start},
            "status": "completed"
        })

        return {
            "timestamp": now.isoformat(),
            "overview": {
                "total_users": total_users,
                "users_joined_today": users_joined_today,
                "users_active_today": users_active_today,
                "total_task_sets": total_task_sets,
                "task_sets_created_today": task_sets_created_today,
                "task_sets_completed_today": task_sets_completed_today
            }
        }

    except Exception as e:
        loggers.error(f"Error getting dashboard overview: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard overview: {str(e)}")


@router.get("/dashboard/users")
async def get_users_metrics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD), defaults to 7 days ago"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD), defaults to today"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get user metrics for date range with daily breakdown for graphs.
    Default range is past 7 days.

    Returns:
        Dictionary containing user metrics with daily breakdown
    """
    try:
        loggers.info(f"Getting user metrics from {start_date} to {end_date}")

        # Parse dates or use defaults
        now = datetime.now(timezone.utc)

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        else:
            end_dt = now

        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            start_dt = end_dt - timedelta(days=7)

        # Get database collection
        users_collection = user_tenant.async_db.users

        # Daily user registrations pipeline
        daily_registrations_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$created_at"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]

        # Daily active users pipeline
        daily_active_pipeline = [
            {
                "$match": {
                    "last_login": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$last_login"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]

        # Users by role pipeline
        users_by_role_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": "$role",
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"count": -1}}
        ]

        # Execute aggregations
        daily_registrations = await (await users_collection.aggregate(daily_registrations_pipeline)).to_list(None)
        daily_active = await (await users_collection.aggregate(daily_active_pipeline)).to_list(None)
        users_by_role = await (await users_collection.aggregate(users_by_role_pipeline)).to_list(None)

        # Get total counts
        total_users = await users_collection.count_documents({})
        users_in_period = await users_collection.count_documents({
            "created_at": {"$gte": start_dt, "$lte": end_dt}
        })

        return {
            "timestamp": now.isoformat(),
            "date_range": {
                "start_date": start_dt.strftime("%Y-%m-%d"),
                "end_date": end_dt.strftime("%Y-%m-%d")
            },
            "summary": {
                "total_users": total_users,
                "users_in_period": users_in_period
            },
            "daily_data": {
                "registrations": daily_registrations,
                "active_users": daily_active
            },
            "distribution": {
                "by_role": users_by_role
            }
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        loggers.error(f"Error getting user metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user metrics: {str(e)}")


@router.get("/dashboard/task-sets")
async def get_task_sets_metrics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD), defaults to today"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD), defaults to today"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get task sets metrics for date range with task type differentiation.
    Default is today's date for both start and end.

    Returns:
        Dictionary containing task sets metrics including primary/followup/curated breakdown
    """
    try:
        loggers.info(f"Getting task sets metrics from {start_date} to {end_date}")

        # Parse dates or use today
        now = datetime.now(timezone.utc)

        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            start_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            start_dt = now.replace(hour=0, minute=0, second=0, microsecond=0)

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        else:
            end_dt = now.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Get database collections
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items

        # Task sets metrics pipeline with task type differentiation
        task_sets_pipeline = [
            {
                "$facet": {
                    "total_task_sets": [{"$count": "count"}],
                    "created_in_range": [
                        {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                        {"$count": "count"}
                    ],
                    "completed_in_range": [
                        {
                            "$match": {
                                "completed_at": {"$gte": start_dt, "$lte": end_dt},
                                "status": "completed"
                            }
                        },
                        {"$count": "count"}
                    ],
                    "by_generation_type": [
                        {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                        {"$group": {"_id": "$gentype", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "by_input_type": [
                        {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "by_status": [
                        {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "daily_creation": [
                        {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                        {
                            "$group": {
                                "_id": {
                                    "$dateToString": {
                                        "format": "%Y-%m-%d",
                                        "date": "$created_at"
                                    }
                                },
                                "count": {"$sum": 1}
                            }
                        },
                        {"$sort": {"_id": 1}}
                    ],
                    "performance_stats": [
                        {
                            "$match": {
                                "created_at": {"$gte": start_dt, "$lte": end_dt},
                                "status": "completed"
                            }
                        },
                        {
                            "$group": {
                                "_id": None,
                                "avg_score": {"$avg": "$scored"},
                                "avg_total_score": {"$avg": "$total_score"},
                                "avg_completion_time": {
                                    "$avg": {
                                        "$subtract": ["$completed_at", "$created_at"]
                                    }
                                },
                                "total_tasks_completed": {"$sum": "$total_tasks"},
                                "total_score_earned": {"$sum": "$scored"}
                            }
                        }
                    ]
                }
            }
        ]

        # Task items created in date range (by generation type)
        task_items_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$facet": {
                    "total_created": [{"$count": "count"}],
                    "by_type": [
                        {"$group": {"_id": "$type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "by_verification_status": [
                        {"$group": {"_id": "$verification_status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ]
                }
            }
        ]

        # Execute aggregations
        task_sets_results = await (await task_sets_collection.aggregate(task_sets_pipeline)).to_list(1)
        task_items_results = await (await task_items_collection.aggregate(task_items_pipeline)).to_list(1)

        # Extract results
        task_sets_data = task_sets_results[0] if task_sets_results else {}
        task_items_data = task_items_results[0] if task_items_results else {}

        # Helper function to safely get count
        def safe_count(data_list):
            return data_list[0]["count"] if data_list and len(data_list) > 0 else 0

        # Get total counts for context
        total_task_items = await task_items_collection.count_documents({})

        return {
            "timestamp": now.isoformat(),
            "date_range": {
                "start_date": start_dt.strftime("%Y-%m-%d"),
                "end_date": end_dt.strftime("%Y-%m-%d")
            },
            "summary": {
                "total_task_sets_all_time": safe_count(task_sets_data.get("total_task_sets", [])),
                "total_task_items_all_time": total_task_items,
                "task_sets_created_in_range": safe_count(task_sets_data.get("created_in_range", [])),
                "task_sets_completed_in_range": safe_count(task_sets_data.get("completed_in_range", [])),
                "task_items_created_in_range": safe_count(task_items_data.get("total_created", []))
            },
            "task_sets": {
                "by_generation_type": task_sets_data.get("by_generation_type", []),
                "by_input_type": task_sets_data.get("by_input_type", []),
                "by_status": task_sets_data.get("by_status", []),
                "daily_creation": task_sets_data.get("daily_creation", []),
                "performance_stats": task_sets_data.get("performance_stats", [])
            },
            "task_items": {
                "by_type": task_items_data.get("by_type", []),
                "by_verification_status": task_items_data.get("by_verification_status", [])
            }
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        loggers.error(f"Error getting task sets metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve task sets metrics: {str(e)}")
