"""
MongoDB Aggregation Pipeline for Task Sets Hierarchical Data
Creates sunburst/nested data structure:
Total → Generation Type → Task Items → Item Types
"""

def create_task_sets_hierarchical_pipeline(start_dt, end_dt):
    """
    Creates a comprehensive aggregation pipeline for task sets with hierarchical breakdown.
    
    Structure:
    1. Total task sets count
    2. By generation type (curated/primary/followup) 
    3. For each type → task items count and breakdown by item types
    
    Args:
        start_dt: Start datetime
        end_dt: End datetime
    
    Returns:
        MongoDB aggregation pipeline
    """
    
    pipeline = [
        # Stage 1: Match task sets in date range
        {
            "$match": {
                "created_at": {"$gte": start_dt, "$lte": end_dt}
            }
        },
        
        # Stage 2: Use gentype field directly (no need for complex conditions)
        # gentype field already contains: "primary", "follow_up", "curated", etc.
        
        # Stage 3: Lookup task items for each task set
        {
            "$lookup": {
                "from": "task_items",
                "localField": "tasks",
                "foreignField": "_id",
                "as": "task_items_details"
            }
        },
        
        # Stage 4: Unwind task items to process individually
        {
            "$unwind": {
                "path": "$task_items_details",
                "preserveNullAndEmptyArrays": True
            }
        },
        
        # Stage 5: Group by generation type and task item type
        {
            "$group": {
                "_id": {
                    "generation_type": "$generation_type",
                    "task_item_type": "$task_items_details.type"
                },
                "task_sets_count": {"$addToSet": "$_id"},
                "task_items_count": {"$sum": 1},
                "task_set_ids": {"$addToSet": "$_id"}
            }
        },
        
        # Stage 6: Add task sets count (unique count)
        {
            "$addFields": {
                "unique_task_sets_count": {"$size": "$task_sets_count"}
            }
        },
        
        # Stage 7: Group by generation type to get totals
        {
            "$group": {
                "_id": "$_id.generation_type",
                "total_task_sets": {"$sum": "$unique_task_sets_count"},
                "total_task_items": {"$sum": "$task_items_count"},
                "task_item_breakdown": {
                    "$push": {
                        "item_type": "$_id.task_item_type",
                        "count": "$task_items_count",
                        "task_sets_with_this_type": "$unique_task_sets_count"
                    }
                }
            }
        },
        
        # Stage 8: Sort task item breakdown by count
        {
            "$addFields": {
                "task_item_breakdown": {
                    "$sortArray": {
                        "input": "$task_item_breakdown",
                        "sortBy": {"count": -1}
                    }
                }
            }
        },
        
        # Stage 9: Group all to get final structure
        {
            "$group": {
                "_id": None,
                "total_task_sets_all_types": {"$sum": "$total_task_sets"},
                "total_task_items_all_types": {"$sum": "$total_task_items"},
                "generation_type_breakdown": {
                    "$push": {
                        "generation_type": "$_id",
                        "task_sets_count": "$total_task_sets",
                        "task_items_count": "$total_task_items",
                        "task_item_types": "$task_item_breakdown"
                    }
                }
            }
        },
        
        # Stage 10: Sort generation types by task sets count
        {
            "$addFields": {
                "generation_type_breakdown": {
                    "$sortArray": {
                        "input": "$generation_type_breakdown",
                        "sortBy": {"task_sets_count": -1}
                    }
                }
            }
        },
        
        # Stage 11: Project final structure
        {
            "$project": {
                "_id": 0,
                "summary": {
                    "total_task_sets": "$total_task_sets_all_types",
                    "total_task_items": "$total_task_items_all_types"
                },
                "hierarchy": "$generation_type_breakdown"
            }
        }
    ]
    
    return pipeline


def create_simple_totals_pipeline(start_dt, end_dt):
    """
    Simple pipeline to get just the totals for comparison.
    """
    
    pipeline = [
        {
            "$facet": {
                "total_task_sets": [
                    {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                    {"$count": "count"}
                ],
                "by_generation_type": [
                    {"$match": {"created_at": {"$gte": start_dt, "$lte": end_dt}}},
                    {
                        "$addFields": {
                            "generation_type": {
                                "$cond": [
                                    {"$ne": ["$theme_id", None]}, "curated",
                                    {"$cond": [
                                        {"$eq": ["$gentype", "follow_up"]}, "followup",
                                        "primary"
                                    ]}
                                ]
                            }
                        }
                    },
                    {"$group": {"_id": "$generation_type", "count": {"$sum": 1}}},
                    {"$sort": {"count": -1}}
                ]
            }
        }
    ]
    
    return pipeline


# Example usage and expected output structure:
"""
Expected Output Structure:

{
  "summary": {
    "total_task_sets": 150,
    "total_task_items": 600
  },
  "hierarchy": [
    {
      "generation_type": "primary",
      "task_sets_count": 100,
      "task_items_count": 400,
      "task_item_types": [
        {
          "item_type": "multiple_choice",
          "count": 200,
          "task_sets_with_this_type": 80
        },
        {
          "item_type": "single_choice", 
          "count": 150,
          "task_sets_with_this_type": 75
        },
        {
          "item_type": "speak_word",
          "count": 50,
          "task_sets_with_this_type": 25
        }
      ]
    },
    {
      "generation_type": "curated",
      "task_sets_count": 30,
      "task_items_count": 120,
      "task_item_types": [
        {
          "item_type": "single_choice",
          "count": 80,
          "task_sets_with_this_type": 25
        },
        {
          "item_type": "multiple_choice",
          "count": 40,
          "task_sets_with_this_type": 20
        }
      ]
    },
    {
      "generation_type": "followup",
      "task_sets_count": 20,
      "task_items_count": 80,
      "task_item_types": [
        {
          "item_type": "speak_word",
          "count": 50,
          "task_sets_with_this_type": 15
        },
        {
          "item_type": "single_choice",
          "count": 30,
          "task_sets_with_this_type": 12
        }
      ]
    }
  ]
}

This creates a perfect sunburst/hierarchical structure:
- Level 1: Total (150 task sets, 600 items)
- Level 2: Generation Type (primary: 100, curated: 30, followup: 20)
- Level 3: Task Item Types (multiple_choice: 200, single_choice: 150, etc.)
"""
